import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/product_model.dart';
import '../services/user_profile_service.dart';
import '../services/otp_service.dart';
import '../widgets/order_confirmation_dialog.dart';
import '../screens/pin_setup_screen.dart';
import '../screens/pin_verification_screen.dart';
import 'package:provider/provider.dart';
import '../services/cart_provider.dart';

class SmartCheckoutBottomSheet extends StatefulWidget {
  final List<Product> products;
  final double totalPrice;
  final String apartmentName;
  final String blockName;
  final String houseNumber;

  const SmartCheckoutBottomSheet({
    super.key,
    required this.products,
    required this.totalPrice,
    required this.apartmentName,
    required this.blockName,
    required this.houseNumber,
  });

  @override
  State<SmartCheckoutBottomSheet> createState() => _SmartCheckoutBottomSheetState();
}

class _SmartCheckoutBottomSheetState extends State<SmartCheckoutBottomSheet> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _otpController = TextEditingController();

  bool _isLoading = false;
  bool _codeSent = false;
  bool _userExists = false;
  String? _verificationId;
  UserProfile? _userProfile;

  @override
  void initState() {
    super.initState();
    _phoneController.text = '+91'; // Set default country code
    _checkUserExists();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _otpController.dispose();
    super.dispose();
  }

  Future<void> _checkUserExists() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final userExists = await UserProfileService.userExistsForAddress(
        apartmentName: widget.apartmentName,
        blockName: widget.blockName,
        houseNumber: widget.houseNumber,
      );

      if (userExists) {
        final profile = await UserProfileService.getUserProfile(
          apartmentName: widget.apartmentName,
          blockName: widget.blockName,
          houseNumber: widget.houseNumber,
        );
        setState(() {
          _userExists = true;
          _userProfile = profile;
        });
        _showPinVerification();
      } else {
        setState(() {
          _userExists = false;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error checking user: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showPinVerification() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PinVerificationScreen(
          apartmentName: widget.apartmentName,
          blockName: widget.blockName,
          houseNumber: widget.houseNumber,
          onPinVerified: () {
            Navigator.of(context).pop(); // Close PIN screen
            _confirmOrder(); // Place order directly
          },
        ),
      ),
    );
  }

  Future<void> _verifyPhoneNumber() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final result = await OTPService.sendOTP(_phoneController.text);

      setState(() {
        _isLoading = false;
      });

      if (result.success) {
        setState(() {
          _verificationId = result.verificationId!;
          _codeSent = true;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.message),
              backgroundColor: result.isDevelopmentMode ? Colors.orange : Colors.green,
              duration: Duration(seconds: result.isDevelopmentMode ? 5 : 3),
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.message),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sending OTP: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _signInWithOTP() async {
    if (_otpController.text.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please enter the OTP')),
        );
      }
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final isValid = await OTPService.verifyOTP(_verificationId!, _otpController.text);

      setState(() {
        _isLoading = false;
      });

      if (isValid) {
        _showPinSetup();
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                OTPService.isDevelopmentMode
                  ? 'Invalid OTP. Use ${OTPService.testOTP} for testing.'
                  : 'Invalid OTP. Please try again.'
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('An error occurred during OTP verification: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showPinSetup() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PinSetupScreen(
          name: _nameController.text.trim(),
          phoneNumber: _phoneController.text.trim(),
          apartmentName: widget.apartmentName,
          blockName: widget.blockName,
          houseNumber: widget.houseNumber,
          onPinSetup: () {
            Navigator.of(context).pop(); // Close PIN setup screen
            _confirmOrder(); // Place order
          },
        ),
      ),
    );
  }

  Future<void> _confirmOrder() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get user profile for order details
      final profile = await UserProfileService.getUserProfile(
        apartmentName: widget.apartmentName,
        blockName: widget.blockName,
        houseNumber: widget.houseNumber,
      );

      final order = {
        'userName': profile?.name ?? _nameController.text.trim(),
        'userPhone': profile?.phoneNumber ?? _phoneController.text.trim(),
        'apartmentName': widget.apartmentName,
        'blockName': widget.blockName,
        'houseNumber': widget.houseNumber,
        'totalPrice': widget.totalPrice,
        'products': widget.products.map((p) => {
          'name': p.name,
          'quantity': p.quantity,
          'price': p.price,
        }).toList(),
        'timestamp': FieldValue.serverTimestamp(),
      };

      await FirebaseFirestore.instance.collection('orders').add(order);

      if (mounted) {
        // Clear cart
        Provider.of<CartProvider>(context, listen: false).clearCart();

        Navigator.of(context).pop();
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => OrderConfirmationDialog(
          apartmentName: widget.apartmentName,
          blockName: widget.blockName,
          houseNumber: widget.houseNumber,
        ),
      );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to place order: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading && !_userExists) {
      return Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(25)),
        ),
        child: const Padding(
          padding: EdgeInsets.all(40.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 20),
              Text('Checking user profile...'),
            ],
          ),
        ),
      );
    }

    if (_userExists) {
      // This will be handled by the PIN verification screen
      return const SizedBox.shrink();
    }

    // New user flow - show OTP verification form
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(25)),
      ),
      child: Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom + 24,
          left: 24.0,
          right: 24.0,
          top: 24.0,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Handle bar
            Center(
              child: Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
            const SizedBox(height: 20),
            
            Text(
              'Complete Your Order',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.green.shade700,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            
            // Order Summary (same as before)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.green.shade50, Colors.green.shade100],
                ),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Order Summary',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade700,
                        ),
                      ),
                      Text(
                        '${widget.products.length} items',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.green.shade600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Total Amount',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade800,
                        ),
                      ),
                      Text(
                        '₹${widget.totalPrice.toStringAsFixed(2)}',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade800,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),
            
            Form(
              key: _formKey,
              child: Column(
                children: [
                  if (!_codeSent) ...[
                    TextFormField(
                      controller: _nameController,
                      decoration: InputDecoration(
                        labelText: 'Your Name',
                        prefixIcon: Icon(Icons.person, color: Colors.green.shade600),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: Colors.green.shade600, width: 2),
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your name';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _phoneController,
                      decoration: InputDecoration(
                        labelText: 'Phone Number (e.g., +************)',
                        prefixIcon: Icon(Icons.phone, color: Colors.green.shade600),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: Colors.green.shade600, width: 2),
                        ),
                      ),
                      keyboardType: TextInputType.phone,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your phone number';
                        }
                        if (!RegExp(r'^\+91[6-9]\d{9}$').hasMatch(value)) {
                          return 'Enter a valid Indian phone number (e.g., +************)';
                        }
                        return null;
                      },
                    ),
                  ],
                  
                  if (_codeSent) ...[
                    TextFormField(
                      controller: _otpController,
                      decoration: InputDecoration(
                        labelText: 'Enter 6-digit OTP',
                        prefixIcon: Icon(Icons.sms, color: Colors.green.shade600),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: Colors.green.shade600, width: 2),
                        ),
                      ),
                      keyboardType: TextInputType.number,
                      maxLength: 6,
                      onChanged: (value) {
                        // Auto-validate when 6 digits are entered
                        if (value.length == 6) {
                          _signInWithOTP();
                        }
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter the OTP';
                        }
                        if (value.length != 6) {
                          return 'OTP must be 6 digits';
                        }
                        return null;
                      },
                    ),
                  ],
                  
                  const SizedBox(height: 20),
                  
                  _isLoading
                      ? Center(
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.green.shade600),
                          ),
                        )
                      : Container(
                          width: double.infinity,
                          height: 56,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [Colors.green.shade400, Colors.green.shade600],
                            ),
                            borderRadius: BorderRadius.circular(28),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.green.withValues(alpha: 0.3),
                                spreadRadius: 0,
                                blurRadius: 15,
                                offset: const Offset(0, 5),
                              ),
                            ],
                          ),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              borderRadius: BorderRadius.circular(28),
                              onTap: _codeSent ? _signInWithOTP : _verifyPhoneNumber,
                              child: Center(
                                child: Text(
                                  _codeSent
                                    ? (OTPService.isDevelopmentMode
                                        ? 'Setup PIN (Dev: Use ${OTPService.testOTP})'
                                        : 'Setup PIN (Auto-validates on 6 digits)')
                                    : 'Send OTP',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                  const SizedBox(height: 10),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
