import 'dart:io';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/payment_config_model.dart';
import 'payment_config_service.dart';

class UPIPaymentService {
  // Get payment configuration
  static Future<PaymentConfigModel> _getConfig() async {
    return await PaymentConfigService.getPaymentConfig();
  }
  
  // Generate transaction reference
  static String _generateTransactionRef() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'CF_$timestamp';
  }

  // Check if an app is installed
  static Future<bool> _isAppInstalled(String packageName) async {
    if (Platform.isAndroid) {
      try {
        const platform = MethodChannel('app_checker');
        final bool isInstalled = await platform.invokeMethod('isAppInstalled', packageName);
        return isInstalled;
      } catch (e) {
        return false;
      }
    }
    return true; // For iOS, we'll try to launch and handle the error
  }

  // Create UPI payment URL
  static String _createUpiUrl({
    required String upiId,
    required String name,
    required double amount,
    required String transactionRef,
    required String note,
  }) {
    return 'upi://pay?pa=$upiId&pn=$name&am=$amount&tr=$transactionRef&tn=$note&cu=INR';
  }

  // PhonePe Payment
  static Future<Map<String, dynamic>> payWithPhonePe({
    required double amount,
    required String orderId,
  }) async {
    try {
      final config = await _getConfig();
      final transactionRef = _generateTransactionRef();
      final note = 'Payment for Order $orderId';

      // PhonePe specific URL scheme
      final phonePeUrl = 'phonepe://pay?pa=${config.upiMerchantId}&pn=${config.upiMerchantName}&am=$amount&tr=$transactionRef&tn=$note&cu=INR';

      // Fallback to generic UPI if PhonePe not installed
      final genericUpiUrl = _createUpiUrl(
        upiId: config.upiMerchantId,
        name: config.upiMerchantName,
        amount: amount,
        transactionRef: transactionRef,
        note: note,
      );

      // Try PhonePe first
      if (await canLaunchUrl(Uri.parse(phonePeUrl))) {
        await launchUrl(Uri.parse(phonePeUrl), mode: LaunchMode.externalApplication);
        return {
          'success': true,
          'method': 'PhonePe',
          'transactionRef': transactionRef,
          'message': 'Redirected to PhonePe'
        };
      }

      // Fallback to generic UPI
      if (await canLaunchUrl(Uri.parse(genericUpiUrl))) {
        await launchUrl(Uri.parse(genericUpiUrl), mode: LaunchMode.externalApplication);
        return {
          'success': true,
          'method': 'UPI',
          'transactionRef': transactionRef,
          'message': 'Redirected to UPI app'
        };
      }

      return {
        'success': false,
        'error': 'No UPI app found'
      };
    } catch (e) {
      return {
        'success': false,
        'error': 'Error launching PhonePe: $e'
      };
    }
  }

  // Google Pay Payment
  static Future<Map<String, dynamic>> payWithGooglePay({
    required double amount,
    required String orderId,
  }) async {
    try {
      final transactionRef = _generateTransactionRef();
      final note = 'Payment for Order $orderId';
      
      // Google Pay specific URL scheme
      final googlePayUrl = 'tez://upi/pay?pa=$merchantUpiId&pn=$merchantName&am=$amount&tr=$transactionRef&tn=$note&cu=INR';
      
      // Fallback to generic UPI
      final genericUpiUrl = _createUpiUrl(
        upiId: merchantUpiId,
        name: merchantName,
        amount: amount,
        transactionRef: transactionRef,
        note: note,
      );

      // Try Google Pay first
      if (await canLaunchUrl(Uri.parse(googlePayUrl))) {
        await launchUrl(Uri.parse(googlePayUrl), mode: LaunchMode.externalApplication);
        return {
          'success': true,
          'method': 'Google Pay',
          'transactionRef': transactionRef,
          'message': 'Redirected to Google Pay'
        };
      }
      
      // Fallback to generic UPI
      if (await canLaunchUrl(Uri.parse(genericUpiUrl))) {
        await launchUrl(Uri.parse(genericUpiUrl), mode: LaunchMode.externalApplication);
        return {
          'success': true,
          'method': 'UPI',
          'transactionRef': transactionRef,
          'message': 'Redirected to UPI app'
        };
      }

      return {
        'success': false,
        'error': 'No UPI app found'
      };
    } catch (e) {
      return {
        'success': false,
        'error': 'Error launching Google Pay: $e'
      };
    }
  }

  // Paytm Payment
  static Future<Map<String, dynamic>> payWithPaytm({
    required double amount,
    required String orderId,
  }) async {
    try {
      final transactionRef = _generateTransactionRef();
      final note = 'Payment for Order $orderId';
      
      // Paytm specific URL scheme
      final paytmUrl = 'paytmmp://pay?pa=$merchantUpiId&pn=$merchantName&am=$amount&tr=$transactionRef&tn=$note&cu=INR';
      
      // Fallback to generic UPI
      final genericUpiUrl = _createUpiUrl(
        upiId: merchantUpiId,
        name: merchantName,
        amount: amount,
        transactionRef: transactionRef,
        note: note,
      );

      // Try Paytm first
      if (await canLaunchUrl(Uri.parse(paytmUrl))) {
        await launchUrl(Uri.parse(paytmUrl), mode: LaunchMode.externalApplication);
        return {
          'success': true,
          'method': 'Paytm',
          'transactionRef': transactionRef,
          'message': 'Redirected to Paytm'
        };
      }
      
      // Fallback to generic UPI
      if (await canLaunchUrl(Uri.parse(genericUpiUrl))) {
        await launchUrl(Uri.parse(genericUpiUrl), mode: LaunchMode.externalApplication);
        return {
          'success': true,
          'method': 'UPI',
          'transactionRef': transactionRef,
          'message': 'Redirected to UPI app'
        };
      }

      return {
        'success': false,
        'error': 'No UPI app found'
      };
    } catch (e) {
      return {
        'success': false,
        'error': 'Error launching Paytm: $e'
      };
    }
  }

  // Generic UPI Payment (shows app chooser)
  static Future<Map<String, dynamic>> payWithUPI({
    required double amount,
    required String orderId,
  }) async {
    try {
      final transactionRef = _generateTransactionRef();
      final note = 'Payment for Order $orderId';
      
      final upiUrl = _createUpiUrl(
        upiId: merchantUpiId,
        name: merchantName,
        amount: amount,
        transactionRef: transactionRef,
        note: note,
      );

      if (await canLaunchUrl(Uri.parse(upiUrl))) {
        await launchUrl(Uri.parse(upiUrl), mode: LaunchMode.externalApplication);
        return {
          'success': true,
          'method': 'UPI',
          'transactionRef': transactionRef,
          'message': 'Redirected to UPI app'
        };
      }

      return {
        'success': false,
        'error': 'No UPI app found'
      };
    } catch (e) {
      return {
        'success': false,
        'error': 'Error launching UPI: $e'
      };
    }
  }

  // Get available payment apps
  static Future<List<String>> getAvailablePaymentApps() async {
    final List<String> availableApps = [];
    
    // Check for popular UPI apps
    final apps = {
      'PhonePe': 'phonepe://pay',
      'Google Pay': 'tez://upi/pay',
      'Paytm': 'paytmmp://pay',
      'BHIM': 'bhim://pay',
      'Amazon Pay': 'amazonpay://pay',
    };

    for (final entry in apps.entries) {
      try {
        if (await canLaunchUrl(Uri.parse(entry.value))) {
          availableApps.add(entry.key);
        }
      } catch (e) {
        // App not available
      }
    }

    return availableApps;
  }

  // Verify payment (this would typically involve checking with your backend)
  static Future<Map<String, dynamic>> verifyPayment(String transactionRef) async {
    // In a real app, you would call your backend API to verify the payment
    // For now, we'll simulate a verification process
    
    await Future.delayed(const Duration(seconds: 2));
    
    // Simulate random success/failure for demo
    final isSuccess = DateTime.now().millisecond % 2 == 0;
    
    return {
      'success': isSuccess,
      'transactionRef': transactionRef,
      'status': isSuccess ? 'SUCCESS' : 'FAILED',
      'message': isSuccess ? 'Payment successful' : 'Payment failed'
    };
  }
}
